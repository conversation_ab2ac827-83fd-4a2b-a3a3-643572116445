import os
import json
import traceback
from datetime import datetime
from typing import Dict, Any, Tuple

from utils.mongo_utils import Mongo
from utils.aria_utils import ARIA
from utils.boto3_utils import get_secret
from utils.crud_handler_execution import <PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.crud_bols import CrudBols
from utils.crud_titles import CrudTitles
from utils.crud_invoices import CrudInvoices
from utils.crud_vins import CrudVins
from utils.crud_bre_results import CrudBreResults
from utils.crud_email import CrudEmails
import time

class PostBREProcessor:
    def __init__(self, event: Dict[str, Any], mongo_uri):

        self.event = event
        self.mongo_client = Mongo(mongo_uri)
        
        self.crud_handler = CrudHandler(self.mongo_client)
        self.crud_invoices = CrudInvoices(self.mongo_client)
        self.crud_titles = CrudTitles(self.mongo_client)
        self.crud_bols = CrudBols(self.mongo_client)
        self.crud_emails = CrudEmails(self.mongo_client)
        self.crud_reynols_report = CrudVins(self.mongo_client)
        self.crud_bre_results = CrudBreResults(self.mongo_client)


        self.action = event.get('action', {})
        self.document = event.get('document', {})
        self.app_id = self.document.get('app_id')
        self.document_id = self.document.get('id')
        self.current_status_id = self.document.get('aria_status', '')
        self.status_info = event.get('status', {})
        self.parsed_fields = event.get('parsed_fields', {})
        self.passed_rules = event.get('passed_rules', [])
        self.not_passed_rules = event.get('not_passed_rules', [])
        self.valid_rules = event.get('valid_rules', [])
        self.request_response = event.get('request_response', False)
        self.execution_id = event.get('execution_id', '')
        self.ocr_groups = self.document.get('ocr_groups', [])
        self.bre_type = self.event.get('bre_type', '')

        if self.bre_type == '':
            if "invoice" in self.ocr_groups[0]:
                self.bre_type = {'invoice': os.environ['DEFAULT_BRE_TYPE']}
            if "bol" in self.ocr_groups[0]:
                self.bre_type = {'bol': os.environ['DEFAULT_BRE_TYPE']}
            if "title" in self.ocr_groups[0]:
                self.bre_type = {'title': os.environ['DEFAULT_BRE_TYPE']}

        self.valid_rules.sort()
        self.passed_rules[self.ocr_groups[0]].sort()
        self.not_passed_rules[self.ocr_groups[0]].sort()
        self.rules_to_remove = [93]


        print("VALID RULES", self.valid_rules)
        print("PASSED RULES", self.passed_rules)
        print("NOT PASSED RULES", self.not_passed_rules)
        print("RULES TO REMOVE", self.rules_to_remove)

        
        
        secret = get_secret(secret_name=f'{os.environ["ENV"]}-aria_cm_tokens')
        self.aria = ARIA(base_url=secret[os.environ['ARIA_ENV']]['url'], request_token=secret[os.environ['ARIA_ENV']]['token'])

        if not self.app_id:
            raise ValueError("app_id is missing from the document")

    def get_bol_dates_found(self):
        if "bol" not in self.ocr_groups[0]:
            return []
        
        rows = self.parsed_fields[self.ocr_groups[0]]['dates'].get("rows", {}) or self.parsed_fields[self.ocr_groups[0]]['dates'].get("value", {})
        dates = []
        for k,v in rows.items():
            date_val = v['cells']['date']['value'].replace(" ", "")
            dates.append(date_val)

        return dates

    def process_based_on_rules_bre(self):

        if self.valid_rules == self.passed_rules[self.ocr_groups[0]]:
            aria_exception = ''
            next_status_id = self.action['target_status']
            note = ""
            
            next_status_label = self.status_info[next_status_id]['label']
            next_status = self.status_info[next_status_id]['key']

        else:
            
            next_status_id = self.action['source_status']

            print(self.status_info)
            actual_status = get_status_label_by_status_id(next_status_id, self.status_info).lower()
            next_status = "ready"
            aria_exception = ""
            note = ""

            print("Actual status" , actual_status)

            if "invoice" in self.ocr_groups[0]:

                if "needs" in actual_status:
                    if len(self.not_passed_rules[self.ocr_groups[0]]) > 0:
                        
                        next_status = "needs"
                        aria_exception = "One or more fields require human intervention"
                        
                        if len(self.not_passed_rules[self.ocr_groups[0]]) == 2 and (74 in self.not_passed_rules[self.ocr_groups[0]] and 84 in self.not_passed_rules[self.ocr_groups[0]]) and (80 in self.passed_rules[self.ocr_groups[0]] and 82 in self.passed_rules[self.ocr_groups[0]]):
                            next_status = "hold"
                            aria_exception = "Date for this vehicle not found yet"
                        elif 80 in self.not_passed_rules[self.ocr_groups[0]] and 82 in self.not_passed_rules[self.ocr_groups[0]]:
                            next_status = "needs"
                            aria_exception = "Date missing for more than 60 days"
                        elif (74 in self.passed_rules[self.ocr_groups[0]] or 84 in self.passed_rules[self.ocr_groups[0]]) and len(self.not_passed_rules[self.ocr_groups[0]]) == 1:
                            next_status = "ready"
                            aria_exception = ""                         

                if "hold" in actual_status:
                    if len(self.not_passed_rules[self.ocr_groups[0]]) > 0:

                        next_status = "needs"
                        aria_exception = "One or more fields require human intervention"
                        
                        if len(self.not_passed_rules[self.ocr_groups[0]]) == 2 and (74 in self.not_passed_rules[self.ocr_groups[0]] and 84 in self.not_passed_rules[self.ocr_groups[0]]) and (80 in self.passed_rules[self.ocr_groups[0]] and 82 in self.passed_rules[self.ocr_groups[0]]):
                            next_status = "hold"
                            aria_exception = "Date for this vehicle not found yet"
                        elif 74 in self.not_passed_rules[self.ocr_groups[0]] and 84 in self.not_passed_rules[self.ocr_groups[0]] and (80 in self.not_passed_rules[self.ocr_groups[0]] or 82 in self.not_passed_rules[self.ocr_groups[0]]):
                            next_status = "needs"
                            aria_exception = "Date missing for more than 60 days"
                        elif (74 in self.passed_rules[self.ocr_groups[0]] or 84 in self.passed_rules[self.ocr_groups[0]]) and len(self.not_passed_rules[self.ocr_groups[0]]) == 1:
                            next_status = "ready"
                            aria_exception = ""

                            
            if "bol" in self.ocr_groups[0]:
                next_status = "needs"
                aria_exception = ""

                exceptions = []
                
                if 24 in self.not_passed_rules[self.ocr_groups[0]] and 22 not in self.not_passed_rules[self.ocr_groups[0]]:
                    exceptions.append("VIN(s) not found in DMS")

                if 22 in self.not_passed_rules[self.ocr_groups[0]]:
                    exceptions.append("Extracted VIN(s) is not correct")

                if 21 in self.not_passed_rules[self.ocr_groups[0]]:
                    exceptions.append("BOL date not found")
                
                if 23 in self.not_passed_rules[self.ocr_groups[0]]:
                    exceptions.append("Multiple dates found")

                if 25 in self.not_passed_rules[self.ocr_groups[0]]:
                    exceptions.append("Incorrect address")

                aria_exception = ", ".join(exceptions) if exceptions else ""
                    

            if "title" in self.ocr_groups[0]:
                next_status = "needs"
                aria_exception = "Needs human review"

            if next_status is not None:
                next_status_id, next_status_label = get_status_label_info(next_status, self.status_info)


        if "invoice" in self.ocr_groups[0]:
            if "ready" in next_status_label.lower():
                self.parsed_fields[self.ocr_groups[0]]["reynols_report"]['display'] = True
                self.parsed_fields[self.ocr_groups[0]]["stock_in_values"]['display'] = True
            else:
                self.parsed_fields[self.ocr_groups[0]]["reynols_report"]['display'] = False
                self.parsed_fields[self.ocr_groups[0]]["stock_in_values"]['display'] = False

        return next_status_id, next_status_label, aria_exception, note


    def process_default_bre(self):

        next_status = get_status_label_by_status_id(self.action['source_status'], self.status_info)
        aria_exception = ""
        note = ""
        
        if "invoice" in self.ocr_groups[0]:
            next_status = "ready"
            aria_exception = ""
            note = ""

            if len(self.not_passed_rules[self.ocr_groups[0]]) > 0:
                next_status = "needs"
                aria_exception = "One or more fields require human intervention"

                if (74 in self.passed_rules[self.ocr_groups[0]] or 84 in self.passed_rules[self.ocr_groups[0]]) and len(self.not_passed_rules[self.ocr_groups[0]]) == 1:
                    next_status = "ready"
                    aria_exception = ""

                if len(self.not_passed_rules[self.ocr_groups[0]]) == 2 and (74 in self.not_passed_rules[self.ocr_groups[0]] and 84 in self.not_passed_rules[self.ocr_groups[0]]) and (80 in self.passed_rules[self.ocr_groups[0]] and 82 in self.passed_rules[self.ocr_groups[0]]):
                    next_status = "hold"
                    aria_exception = "Date for this vehicle not found yet"

                    self.aria.create_event(
                        app_id=self.document['app_id'],
                        item_id=self.document['id'],
                        title="Date for this vehicle not found yet",
                        status=2
                    )

                if 74 in self.not_passed_rules[self.ocr_groups[0]] and 84 in self.not_passed_rules[self.ocr_groups[0]] and (80 in self.not_passed_rules[self.ocr_groups[0]] or 82 in self.not_passed_rules[self.ocr_groups[0]]):
                    next_status = "needs"
                    aria_exception = "No date found after 60 days on hold"

        if "bol" in self.ocr_groups[0]:
            next_status = "complete"
            aria_exception = ""
            note = ""
            body_exception = ""

            if len(self.not_passed_rules[self.ocr_groups[0]]) > 0:
                next_status = "needs"

                exceptions = []
                
                if 24 in self.not_passed_rules[self.ocr_groups[0]] and 22 not in self.not_passed_rules[self.ocr_groups[0]]:
                    exceptions.append("VIN(s) not found in DMS")

                if 22 in self.not_passed_rules[self.ocr_groups[0]]:
                    exceptions.append("Extracted VIN(s) is not correct")

                if 21 in self.not_passed_rules[self.ocr_groups[0]]:
                    exceptions.append("BOL date not found")
                
                if 23 in self.not_passed_rules[self.ocr_groups[0]]:
                    exceptions.append("Multiple dates found")
                    dates_found = self.get_bol_dates_found()
                    body_exception = "Multiple dates found: \n"
                    date_concat = "\n".join(dates_found)
                    body_exception += date_concat

                if 25 in self.not_passed_rules[self.ocr_groups[0]]:
                    exceptions.append("Incorrect address")

                aria_exception = ", ".join(exceptions) if exceptions else ""

            bol_data = self.crud_bols.find_bol_by_wi_id(self.document_id)
            email_data = self.crud_emails.find_email_by_id(bol_data['email_id'])
            print(email_data)
            title = f"""Email info:"""

            body = f"""
            Received: {email_data['createdDateTime']}\n
            From: {email_data['sender']}\n
            Subject: {email_data['subject']}\n
            Attachment name: {bol_data['attachment_name']}\n
            """

            self.aria.create_event(
                app_id=self.document['app_id'],
                item_id=self.document['id'],
                title=title,
                body=body,
                status=0
            )

            if aria_exception != "" and body_exception != "":
                self.aria.create_event(
                    app_id=self.document['app_id'],
                    item_id=self.document['id'],
                    title="Exception",
                    body=body_exception,
                    status=2
                )

        if "title" in self.ocr_groups[0]:
            next_status = "needs"
            aria_exception = ""
            note = ""

            if len(self.not_passed_rules[self.ocr_groups[0]]) > 0:
                next_status = "needs"
                aria_exception = "Not all rules required have passed"

            self.aria.create_event(
                app_id=self.document['app_id'],
                item_id=self.document['id'],
                title="Please review all titles and pages are correct",
                status=2
            )
            
        next_status_id, next_status_label = get_status_label_info(next_status, self.status_info)

        if "invoice" in self.ocr_groups[0]:
            if "ready" in next_status_label.lower():
                self.parsed_fields[self.ocr_groups[0]]["reynols_report"]['display'] = True
                self.parsed_fields[self.ocr_groups[0]]["stock_in_values"]['display'] = True
            else:
                self.parsed_fields[self.ocr_groups[0]]["reynols_report"]['display'] = False
                self.parsed_fields[self.ocr_groups[0]]["stock_in_values"]['display'] = False

        return next_status_id, next_status_label, aria_exception, note
    
    def save_post_iteration_bre_results(self, target_status_label):
        bre_results = self.crud_bre_results.find_bre_results_by_wi_id(self.document_id)
        if bre_results:
            iteration = bre_results['total_iterations']
            self.crud_bre_results.update_bre_results_by_wi_id(
                filter={"aria_wi_id": self.document_id},
                data={"$set": {
                f"iterations.{iteration}.time": datetime.now(),
                f"iterations.{iteration}.input": self.event,
                f"iterations.{iteration}.output": self.parsed_fields,
                f"iterations.{iteration}.passed_rules": self.passed_rules,
                f"iterations.{iteration}.not_passed_rules": self.not_passed_rules,
                f"iterations.{iteration}.success": True,
                f"iterations.{iteration}.error_message": '',
                f"iterations.{iteration}.target_status": target_status_label
            }})

    def update_invoice(self, next_status_label):
        update_bre_results = True
        if "invoice" in self.ocr_groups[0]:
            rows_report = self.parsed_fields[self.ocr_groups[0]]['reynols_report']['rows'] or self.parsed_fields[self.ocr_groups[0]]['fields']['reynols_report']['value']
            vin = ''
            for k, v in rows_report.items():
                if v['cells']['field']['value'] == 'VIN':
                    vin = v['cells']['value']['value']
                    break

            if vin != '':
                self.crud_reynols_report.update_row_by_vin(
                    vin=vin, 
                    data_to_set={
                        "flows.post-inventory.docs.invoice.fields": self.parsed_fields[self.ocr_groups[0]],
                        "flows.post-inventory.docs.invoice.aria_data.status": next_status_label,
                        "flows.post-inventory.docs.invoice.updated_at": datetime.now(),
                        "updated_at": datetime.now()
                    },
                    data_to_push={"flows.post-inventory.docs.invoice.aria_data.status_history": next_status_label})
                
                vin_status = 6
                if "ready" in next_status_label.lower():
                    vin_status = 7
                
                self.crud_reynols_report.update_row_by_vin(vin=vin, data_to_set={"status": vin_status}, data_to_push={"status_history": vin_status})

            self.crud_invoices.update_invoice_by_wi_id(self.document_id, data_to_set={"fields": self.parsed_fields[self.ocr_groups[0]], "status": next_status_label, "updated_at": datetime.now()}, data_to_push={"status_history": next_status_label})
    
        return update_bre_results
    
    def update_bol(self, next_status_label, aria_exception):
        update_bre_results = True
        if "bol" in self.ocr_groups[0]:
            rows_table = self.parsed_fields[self.ocr_groups[0]].get('bol_vins', {}).get('rows', {})
            if len(list(rows_table.keys())) == 0:
                rows_table = self.parsed_fields[self.ocr_groups[0]].get('bol_vins', {}).get('value', {})

            vin_vals = []
            for k, v in rows_table.items():
                vin_val = v['cells']['vin']['value']
                vin_vals.append(vin_val)

            self.crud_bols.update_bol_by_wi_id(self.document_id, data_to_set={"extracted_vins": vin_vals, "fields": self.parsed_fields[self.ocr_groups[0]], "status": next_status_label, "updated_at": datetime.now()}, data_to_push={"status_history": next_status_label})

            actual_status_id = self.action['source_status']
            actual_status = get_status_label_by_status_id(actual_status_id, self.status_info).lower()

            if actual_status == next_status_label.lower() and "VIN(s) not found in DMS".lower() in aria_exception.lower():
                update_bre_results = False

        return update_bre_results
    
    def update_title(self, next_status_label):
        update_bre_results = True
        if "title" in self.ocr_groups[0]:
            rows_table = self.parsed_fields[self.ocr_groups[0]].get('vins', {}).get('rows', {})
            if len(list(rows_table.keys())) == 0:
                rows_table = self.parsed_fields[self.ocr_groups[0]].get('vins', {}).get('value', {})

            vin_vals = []
            for k, v in rows_table.items():
                vin_val = v['cells']['vin']['value']
                vin_vals.append(vin_val)
        
            self.crud_titles.update_title_by_wi_id(self.document_id, data_to_set={"extracted_vins": vin_vals, "fields": self.parsed_fields[self.ocr_groups[0]], "status": next_status_label, "updated_at": datetime.now()}, data_to_push={"status_history": next_status_label})
    
        return update_bre_results
    
    def execute(self):

        self.valid_rules = list(set(self.valid_rules) - set(self.rules_to_remove))
        self.passed_rules[self.ocr_groups[0]] = list(set(self.passed_rules[self.ocr_groups[0]]) - set(self.rules_to_remove))
        self.not_passed_rules[self.ocr_groups[0]] = list(set(self.not_passed_rules[self.ocr_groups[0]]) - set(self.rules_to_remove))
        
        try:

            if self.bre_type[self.ocr_groups[0]] == os.environ['DEFAULT_BRE_TYPE']:
                next_status_id, next_status_label, aria_exception, note = self.process_default_bre()
            else:
                next_status_id, next_status_label, aria_exception, note = self.process_based_on_rules_bre()

            update_bre_results =  True
            if "invoice" in self.ocr_groups[0]:
                update_bre_results = self.update_invoice(next_status_label)
            if "bol" in self.ocr_groups[0]:
                update_bre_results = self.update_bol(next_status_label, aria_exception)
            if "title" in self.ocr_groups[0]:
                update_bre_results = self.update_title(next_status_label)
            
            print("********** PARSED FIELDS ", self.parsed_fields)

            if update_bre_results:
                self.save_post_iteration_bre_results(next_status_label)

            print("REQUEST RESPONSE", self.request_response)


            print("********* TO STATE ", next_status_label)

            if not self.request_response:
        
                self.aria.bre_reply(
                    app_id=self.app_id,
                    item_id=self.document_id,
                    bre_response={
                        "aria_status":{"value": next_status_id},
                        "aria_exception":{"value": aria_exception},
                        self.ocr_groups[0]: self.parsed_fields[self.ocr_groups[0]]
                    })
            
            return {
                'statusCode': 200,
                'body': json.dumps({
                                "aria_status":{"value": next_status_id},
                                "aria_exception":{"value": aria_exception},
                                self.ocr_groups[0]: self.parsed_fields[self.ocr_groups[0]]
                        })
            }


        except Exception as e:
            print('Error: {}'.format(traceback.format_exc()))
            self.crud_handler.mark_as_failed(execution_id=self.execution_id)
            return {
                'statusCode': 500,
                'body': json.dumps({'error': str(e)})
            }

# Utility Functions
def get_status_label_info(status: str, status_info: Dict[str, Any]) -> Tuple[str, str]:
    for k, v in status_info.items():
        if status in v['label'].lower():
            return k, v['label']
    return '', ''

def get_status_label_by_status_id(status_id: str, status_info: Dict[str, Any]) -> Tuple[str, str]:
    for k, v in status_info.items():
        if k == status_id:
            return v['label']
    return ''